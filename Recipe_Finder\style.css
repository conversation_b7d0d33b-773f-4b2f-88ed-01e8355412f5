* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Color Palette */
:root {
  --primary: #ff7e5f;
  --primary-dark: #eb5e41;
  --primary-light: #ffb199;
  --secondary: #0ba360;
  --text-dark: #333333;
  --text-light: #f8f9fa;
  --background: #ffffff;
  --background-light: #f8f9fa;
  --card-bg: #ffffff;
  --border-radius: 8px;
  --shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
  --danger: #dc3545;
  --success: #28a745;
  --warning: #ffc107;
  --info: #17a2b8;
}

/* Dark Mode Variables */
[data-theme="dark"] {
  --text-dark: #f8f9fa;
  --text-light: #333333;
  --background: #1a1a1a;
  --background-light: #2d2d2d;
  --card-bg: #2d2d2d;
  --shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

body {
  font-family: sans-serif;
  background-color: var(--background-light);
  color: var(--text-dark);
  line-height: 1.6;
}

.container {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

header {
  text-align: center;
  margin-bottom: 2rem;
}

.header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

h1 {
  font-size: 2.2rem;
  color: var(--primary);
  margin: 0;
}

h1 i {
  margin-right: 10px;
}

header p {
  color: var(--text-dark);
  opacity: 0.7;
}

/* Theme Toggle */
.theme-toggle {
  background: none;
  border: 2px solid var(--primary);
  color: var(--primary);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.theme-toggle:hover {
  background-color: var(--primary);
  color: white;
}

/* Navigation Tabs */
.nav-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
  border-bottom: 2px solid var(--background-light);
}

.nav-tab {
  background: none;
  border: none;
  padding: 1rem 1.5rem;
  cursor: pointer;
  color: var(--text-dark);
  opacity: 0.7;
  transition: var(--transition);
  border-bottom: 3px solid transparent;
  font-size: 1rem;
}

.nav-tab.active {
  opacity: 1;
  border-bottom-color: var(--primary);
  color: var(--primary);
}

.nav-tab:hover {
  opacity: 1;
}

.nav-tab i {
  margin-right: 8px;
}

/* Tab Content */
.tab-content {
  display: block;
}

.tab-content.hidden {
  display: none;
}

/* Search Options */
.search-options {
  margin-bottom: 2rem;
}

.search-type-toggle {
  display: flex;
  justify-content: center;
  margin: 1rem 0;
  gap: 0;
}

.search-type-btn {
  background-color: var(--background-light);
  color: var(--text-dark);
  border: 1px solid var(--primary);
  padding: 8px 16px;
  cursor: pointer;
  transition: var(--transition);
  font-size: 0.9rem;
}

.search-type-btn:first-child {
  border-radius: var(--border-radius) 0 0 var(--border-radius);
}

.search-type-btn:last-child {
  border-radius: 0 var(--border-radius) var(--border-radius) 0;
  border-left: none;
}

.search-type-btn.active {
  background-color: var(--primary);
  color: white;
}

.search-type-btn:hover {
  background-color: var(--primary-light);
}

#ingredient-search-container {
  margin-top: 1rem;
}

#ingredient-search-container input {
  width: 100%;
  margin-bottom: 10px;
}

/* Quick Actions */
.quick-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.action-btn {
  background-color: var(--secondary);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  padding: 10px 16px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-btn:hover {
  background-color: #0a8f54;
  transform: translateY(-2px);
}

.action-btn.secondary {
  background-color: var(--background-light);
  color: var(--text-dark);
  border: 1px solid #ddd;
}

.action-btn.secondary:hover {
  background-color: #e9ecef;
}

.action-btn.danger {
  background-color: var(--danger);
}

.action-btn.danger:hover {
  background-color: #c82333;
}

.action-btn.small {
  padding: 6px 12px;
  font-size: 0.8rem;
}

/* Filters */
.filters-container {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: var(--shadow);
}

.filters-container h3 {
  margin-bottom: 1rem;
  color: var(--text-dark);
}

.filters {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.filters select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: var(--border-radius);
  background-color: var(--background);
  color: var(--text-dark);
  font-size: 0.9rem;
  min-width: 150px;
}

.filters select:focus {
  outline: none;
  border-color: var(--primary);
}

/* Search History */
.search-history {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 1rem;
}

.history-item {
  background-color: var(--background-light);
  border: 1px solid #ddd;
  border-radius: 20px;
  padding: 6px 12px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: var(--transition);
  color: var(--text-dark);
}

.history-item:hover {
  background-color: var(--primary-light);
  border-color: var(--primary);
}

.search-container {
  display: flex;
  margin-bottom: 2rem;
  gap: 10px;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

input[type="text"] {
  flex: 1;
  padding: 10px 16px;
  font-size: 1rem;
  border-radius: var(--border-radius);
  border: 1px solid #ddd;
  background-color: var(--background);
  color: var(--text-dark);
  outline: none;
}

input[type="text"]:focus {
  border-color: var(--primary);
}

button {
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  padding: 0 20px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
}

button:hover {
  background-color: var(--primary-dark);
}

#result-heading {
  text-align: center;
  margin-bottom: 1.5rem;
  font-size: 1.2rem;
  color: var(--text-dark);
}

.meals-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
}

.meal {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  overflow: hidden;
  transition: var(--transition);
  cursor: pointer;
  position: relative;
}

.meal:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}

.meal img {
  width: 100%;
  height: 180px;
  object-fit: cover;
}

.meal-info {
  padding: 1rem;
}

.meal-title {
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
  color: var(--text-dark);
}

.meal-category {
  display: inline-block;
  background-color: var(--primary-light);
  color: var(--text-dark);
  padding: 3px 8px;
  font-size: 0.8rem;
  border-radius: 20px;
  margin-bottom: 10px;
}

/* Meal Details */
#meal-details {
  background-color: var(--background);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  padding: 1.5rem;
  margin: 2rem 0;
}

.meal-details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.meal-details-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

#back-btn {
  background-color: transparent;
  color: var(--primary);
  border: 1px solid var(--primary);
  padding: 8px 16px;
}

#back-btn:hover {
  background-color: var(--primary);
  color: white;
}

/* Rating System */
.meal-rating {
  text-align: center;
  margin: 1.5rem 0;
  padding: 1rem;
  background-color: var(--background-light);
  border-radius: var(--border-radius);
}

.rating-stars {
  display: flex;
  justify-content: center;
  gap: 5px;
  margin-top: 0.5rem;
}

.star {
  font-size: 1.5rem;
  color: #ddd;
  cursor: pointer;
  transition: var(--transition);
}

.star.filled {
  color: #ffc107;
}

.star:hover {
  color: #ffc107;
  transform: scale(1.1);
}

/* Shopping List */
.shopping-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  margin-bottom: 8px;
  box-shadow: var(--shadow);
  transition: var(--transition);
}

.shopping-item.checked {
  opacity: 0.6;
}

.shopping-item.checked .shopping-item-text {
  text-decoration: line-through;
}

.shopping-item-label {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  flex: 1;
}

.shopping-item-label input[type="checkbox"] {
  margin: 0;
}

.remove-btn {
  background-color: var(--danger);
  color: white;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
}

.remove-btn:hover {
  background-color: #c82333;
  transform: scale(1.1);
}

/* Collections */
.collection-card {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  padding: 1.5rem;
  margin-bottom: 1rem;
  transition: var(--transition);
}

.collection-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.collection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.collection-header h3 {
  margin: 0;
  color: var(--primary);
}

.collection-actions {
  display: flex;
  gap: 0.5rem;
}

.collection-description {
  color: var(--text-dark);
  opacity: 0.8;
  margin-bottom: 1rem;
  font-style: italic;
}

.collection-stats {
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
  color: var(--text-dark);
  opacity: 0.7;
}

.collection-stats i {
  margin-right: 5px;
}

/* Empty States */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--text-dark);
  opacity: 0.6;
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
}

/* Headers for tabs */
.favorites-header,
.collections-header,
.shopping-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.favorites-header h2,
.collections-header h2,
.shopping-list-header h2 {
  margin: 0;
  color: var(--text-dark);
}

.shopping-list-actions {
  display: flex;
  gap: 0.5rem;
}

.meal-details-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.meal-details-img {
  width: 100%;
  max-width: 400px;
  border-radius: var(--border-radius);
  margin-bottom: 1.5rem;
}

.meal-details-title {
  font-size: 1.8rem;
  color: var(--primary);
  margin-bottom: 0.5rem;
  text-align: center;
}

.meal-details-category {
  margin-bottom: 1rem;
  text-align: center;
}

.meal-details-category span {
  background-color: var(--primary-light);
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
}

.meal-details-instructions {
  line-height: 1.7;
  margin-bottom: 1.5rem;
}

.meal-details-instructions h3 {
  margin-bottom: 0.5rem;
}

.ingredients-list {
  list-style-type: none;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 8px;
  margin-bottom: 1.5rem;
  width: 100%;
}

.ingredients-list li {
  display: flex;
  align-items: center;
  padding: 6px 10px;
  background-color: var(--background-light);
  border-radius: var(--border-radius);
  font-size: 0.9rem;
}

.ingredients-list li i {
  color: var(--secondary);
  margin-right: 8px;
}

.youtube-link {
  display: inline-block;
  background-color: #ff0000;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 600;
  margin-top: 1rem;
  font-size: 0.9rem;
}

.youtube-link i {
  margin-right: 8px;
}

#error-container {
  background-color: rgba(255, 126, 95, 0.1);
  border: 1px solid var(--primary);
  color: var(--primary-dark);
  padding: 1rem;
  border-radius: var(--border-radius);
  text-align: center;
  margin-bottom: 1.5rem;
}

.api-link {
  text-align: center;
  padding-bottom: 1.5rem;
  color: var(--text-dark);
  opacity: 0.7;
  font-size: 0.9rem;
}

.api-link a {
  color: var(--primary);
  text-decoration: none;
}

/* Cooking Timer */
.cooking-timer {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 1.5rem;
  min-width: 250px;
  z-index: 1000;
}

.timer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.timer-header h3 {
  margin: 0;
  color: var(--primary);
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-dark);
  opacity: 0.7;
}

.close-btn:hover {
  opacity: 1;
}

.timer-display {
  text-align: center;
  font-size: 2rem;
  font-weight: bold;
  color: var(--primary);
  margin-bottom: 1rem;
  font-family: "Courier New", monospace;
}

.timer-controls {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.timer-controls input {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: var(--border-radius);
  text-align: center;
}

.timer-controls button {
  padding: 8px 16px;
  font-size: 0.9rem;
}

/* Modals */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: var(--background);
  border-radius: var(--border-radius);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--background-light);
}

.modal-header h3 {
  margin: 0;
  color: var(--text-dark);
}

.modal-body {
  padding: 1.5rem;
}

.modal-body input,
.modal-body textarea,
.modal-body select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: var(--border-radius);
  margin-bottom: 1rem;
  background-color: var(--background);
  color: var(--text-dark);
}

.modal-body textarea {
  resize: vertical;
  min-height: 80px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid var(--background-light);
}

/* Share Options */
.share-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.share-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 1rem;
  border: 1px solid #ddd;
  border-radius: var(--border-radius);
  background-color: var(--background);
  color: var(--text-dark);
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
}

.share-btn:hover {
  background-color: var(--background-light);
  transform: translateY(-2px);
}

.share-btn[data-platform="facebook"]:hover {
  background-color: #1877f2;
  color: white;
}

.share-btn[data-platform="twitter"]:hover {
  background-color: #1da1f2;
  color: white;
}

.share-btn[data-platform="whatsapp"]:hover {
  background-color: #25d366;
  color: white;
}

.share-url-container {
  margin-top: 1rem;
}

.share-url-container input {
  margin-bottom: 0;
}

/* Notifications */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: var(--success);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: var(--border-radius);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1001;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.notification.show {
  transform: translateX(0);
}

/* Remove favorite button */
.remove-favorite-btn {
  background-color: var(--danger);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  padding: 6px 12px;
  font-size: 0.8rem;
  cursor: pointer;
  margin-top: 8px;
  transition: var(--transition);
}

.remove-favorite-btn:hover {
  background-color: #c82333;
}

.hidden {
  display: none;
}

@media (max-width: 768px) {
  .header-top {
    flex-direction: column;
    gap: 1rem;
  }

  .nav-tabs {
    flex-wrap: wrap;
  }

  .nav-tab {
    padding: 0.8rem 1rem;
    font-size: 0.9rem;
  }

  .quick-actions {
    flex-direction: column;
    align-items: center;
  }

  .filters {
    flex-direction: column;
    align-items: stretch;
  }

  .filters select {
    min-width: auto;
  }

  .meal-details-header {
    flex-direction: column;
    align-items: stretch;
  }

  .meal-details-actions {
    justify-content: center;
  }

  .favorites-header,
  .collections-header,
  .shopping-list-header {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }

  .collection-header {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
    gap: 1rem;
  }

  .share-options {
    grid-template-columns: repeat(2, 1fr);
  }

  .cooking-timer {
    bottom: 10px;
    right: 10px;
    left: 10px;
    min-width: auto;
  }

  .notification {
    right: 10px;
    left: 10px;
  }
}

@media (max-width: 600px) {
  .search-container {
    flex-direction: column;
  }

  .search-type-toggle {
    flex-direction: column;
  }

  .search-type-btn:first-child {
    border-radius: var(--border-radius) var(--border-radius) 0 0;
  }

  .search-type-btn:last-child {
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    border-left: 1px solid var(--primary);
    border-top: none;
  }

  button#search-btn {
    width: 100%;
    padding: 10px;
  }

  .meals-container {
    grid-template-columns: 1fr;
  }

  .meal-details-img {
    max-width: 100%;
  }

  .ingredients-list {
    grid-template-columns: 1fr;
  }

  .nav-tab {
    padding: 0.6rem 0.8rem;
    font-size: 0.8rem;
  }

  .action-btn {
    padding: 8px 12px;
    font-size: 0.8rem;
  }

  .share-options {
    grid-template-columns: 1fr;
  }

  .modal-content {
    width: 95%;
    margin: 1rem;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 1rem;
  }
}
