/* Import Google Fonts */
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap");

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Modern Color Palette */
:root {
  /* Primary Colors - Elegant Orange to Coral Gradient */
  --primary: linear-gradient(135deg, #ff6b6b, #ff8e53);
  --primary-solid: #ff6b6b;
  --primary-dark: #e55555;
  --primary-light: #ffe0e0;
  --primary-hover: #ff5252;

  /* Secondary Colors - Fresh Green */
  --secondary: linear-gradient(135deg, #4ecdc4, #44a08d);
  --secondary-solid: #4ecdc4;
  --secondary-dark: #3ba99c;
  --secondary-light: #e0f7f5;

  /* Accent Colors */
  --accent: linear-gradient(135deg, #667eea, #764ba2);
  --accent-solid: #667eea;
  --accent-light: #e8ebff;

  /* Neutral Colors */
  --text-primary: #2c3e50;
  --text-secondary: #5a6c7d;
  --text-muted: #8492a6;
  --text-light: #ffffff;

  /* Background Colors */
  --background: #ffffff;
  --background-secondary: #f8fafc;
  --background-tertiary: #f1f5f9;
  --card-bg: #ffffff;
  --overlay: rgba(255, 255, 255, 0.95);

  /* Status Colors */
  --success: #10b981;
  --success-light: #d1fae5;
  --warning: #f59e0b;
  --warning-light: #fef3c7;
  --danger: #ef4444;
  --danger-light: #fee2e2;
  --info: #3b82f6;
  --info-light: #dbeafe;

  /* Design System */
  --border-radius-sm: 6px;
  --border-radius: 12px;
  --border-radius-lg: 16px;
  --border-radius-xl: 24px;

  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);

  /* Spacing */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
}

/* Dark Mode Variables */
[data-theme="dark"] {
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  --text-light: #1e293b;

  --background: #0f172a;
  --background-secondary: #1e293b;
  --background-tertiary: #334155;
  --card-bg: #1e293b;
  --overlay: rgba(15, 23, 42, 0.95);

  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.3),
    0 4px 6px -2px rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.3),
    0 10px 10px -5px rgba(0, 0, 0, 0.2);
}

body {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Oxygen, Ubuntu, Cantarell, sans-serif;
  background: linear-gradient(
    135deg,
    var(--background-secondary) 0%,
    var(--background-tertiary) 100%
  );
  color: var(--text-primary);
  line-height: 1.7;
  font-size: 16px;
  min-height: 100vh;
  position: relative;
}

body::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
      circle at 20% 80%,
      rgba(255, 107, 107, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(78, 205, 196, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 40% 40%,
      rgba(102, 126, 234, 0.05) 0%,
      transparent 50%
    );
  pointer-events: none;
  z-index: -1;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-xl) var(--space-md);
  position: relative;
  z-index: 1;
}

header {
  text-align: center;
  margin-bottom: var(--space-2xl);
  background: var(--card-bg);
  border-radius: var(--border-radius-xl);
  padding: var(--space-xl);
  box-shadow: var(--shadow-lg);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-md);
}

h1 {
  font-size: 3rem;
  font-weight: 800;
  background: var(--primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  letter-spacing: -0.02em;
}

h1 i {
  margin-right: var(--space-sm);
  background: var(--primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

header p {
  color: var(--text-secondary);
  font-size: 1.125rem;
  font-weight: 500;
  margin: 0;
}

/* Theme Toggle */
.theme-toggle {
  background: var(--card-bg);
  border: 2px solid var(--primary-solid);
  color: var(--primary-solid);
  border-radius: 50%;
  width: 48px;
  height: 48px;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow);
  position: relative;
  overflow: hidden;
}

.theme-toggle::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--primary);
  opacity: 0;
  transition: var(--transition);
  z-index: -1;
}

.theme-toggle:hover::before {
  opacity: 1;
}

.theme-toggle:hover {
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.theme-toggle i {
  font-size: 1.125rem;
}

/* API Link */
.api-link {
  text-align: center;
  margin-bottom: var(--space-xl);
  color: var(--text-muted);
  font-size: 0.875rem;
  font-weight: 500;
}

.api-link a {
  color: var(--primary-solid);
  text-decoration: none;
  font-weight: 600;
  transition: var(--transition);
}

.api-link a:hover {
  color: var(--primary-hover);
}

/* Navigation Tabs */
.nav-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: var(--space-2xl);
  background: var(--card-bg);
  border-radius: var(--border-radius-lg);
  padding: var(--space-xs);
  box-shadow: var(--shadow);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  gap: var(--space-xs);
}

.nav-tab {
  background: transparent;
  border: none;
  padding: var(--space-md) var(--space-lg);
  cursor: pointer;
  color: var(--text-secondary);
  transition: var(--transition);
  border-radius: var(--border-radius);
  font-size: 0.9rem;
  font-weight: 600;
  position: relative;
  overflow: hidden;
  white-space: nowrap;
}

.nav-tab::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--primary);
  opacity: 0;
  transition: var(--transition);
  z-index: -1;
}

.nav-tab.active::before {
  opacity: 1;
}

.nav-tab.active {
  color: white;
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.nav-tab:hover:not(.active) {
  color: var(--primary-solid);
  background: var(--primary-light);
}

.nav-tab i {
  margin-right: var(--space-sm);
  font-size: 0.875rem;
}

/* Tab Content */
.tab-content {
  display: block;
}

.tab-content.hidden {
  display: none;
}

/* Search Options */
.search-options {
  margin-bottom: var(--space-xl);
  background: var(--card-bg);
  border-radius: var(--border-radius-lg);
  padding: var(--space-xl);
  box-shadow: var(--shadow);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.search-container {
  display: flex;
  margin-bottom: var(--space-lg);
  gap: var(--space-sm);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.search-type-toggle {
  display: flex;
  justify-content: center;
  margin: var(--space-lg) 0;
  background: var(--background-secondary);
  border-radius: var(--border-radius);
  padding: var(--space-xs);
  gap: var(--space-xs);
}

.search-type-btn {
  background: transparent;
  color: var(--text-secondary);
  border: none;
  padding: var(--space-sm) var(--space-lg);
  cursor: pointer;
  transition: var(--transition);
  font-size: 0.875rem;
  font-weight: 600;
  border-radius: var(--border-radius-sm);
  position: relative;
  overflow: hidden;
}

.search-type-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--primary);
  opacity: 0;
  transition: var(--transition);
  z-index: -1;
}

.search-type-btn.active::before {
  opacity: 1;
}

.search-type-btn.active {
  color: white;
  box-shadow: var(--shadow-sm);
}

.search-type-btn:hover:not(.active) {
  color: var(--primary-solid);
  background: var(--primary-light);
}

#ingredient-search-container {
  margin-top: var(--space-lg);
}

#ingredient-search-container input {
  width: 100%;
  margin-bottom: var(--space-sm);
}

/* Quick Actions */
.quick-actions {
  display: flex;
  justify-content: center;
  gap: var(--space-md);
  margin-bottom: var(--space-xl);
}

.action-btn {
  background: var(--secondary);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  padding: var(--space-md) var(--space-lg);
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.action-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
  opacity: 0;
  transition: var(--transition);
}

.action-btn:hover::before {
  opacity: 1;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.action-btn.secondary {
  background: var(--background-secondary);
  color: var(--text-primary);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.action-btn.secondary:hover {
  background: var(--background-tertiary);
}

.action-btn.danger {
  background: var(--danger);
}

.action-btn.danger:hover {
  background: #dc2626;
}

.action-btn.small {
  padding: var(--space-sm) var(--space-md);
  font-size: 0.75rem;
}

/* Filters */
.filters-container {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: var(--shadow);
}

.filters-container h3 {
  margin-bottom: 1rem;
  color: var(--text-dark);
}

.filters {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.filters select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: var(--border-radius);
  background-color: var(--background);
  color: var(--text-dark);
  font-size: 0.9rem;
  min-width: 150px;
}

.filters select:focus {
  outline: none;
  border-color: var(--primary);
}

/* Search History */
.search-history {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 1rem;
}

.history-item {
  background-color: var(--background-light);
  border: 1px solid #ddd;
  border-radius: 20px;
  padding: 6px 12px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: var(--transition);
  color: var(--text-dark);
}

.history-item:hover {
  background-color: var(--primary-light);
  border-color: var(--primary);
}

.search-container {
  display: flex;
  margin-bottom: 2rem;
  gap: 10px;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

/* Input Fields */
input[type="text"],
input[type="number"],
textarea,
select {
  flex: 1;
  padding: var(--space-md) var(--space-lg);
  font-size: 1rem;
  border-radius: var(--border-radius);
  border: 2px solid rgba(0, 0, 0, 0.1);
  background-color: var(--background);
  color: var(--text-primary);
  outline: none;
  transition: var(--transition);
  font-family: inherit;
  box-shadow: var(--shadow-sm);
}

input[type="text"]:focus,
input[type="number"]:focus,
textarea:focus,
select:focus {
  border-color: var(--primary-solid);
  box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
  transform: translateY(-1px);
}

input[type="text"]::placeholder,
input[type="number"]::placeholder,
textarea::placeholder {
  color: var(--text-muted);
}

/* Buttons */
button {
  background: var(--primary);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  padding: var(--space-md) var(--space-xl);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  font-family: inherit;
}

button::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
  opacity: 0;
  transition: var(--transition);
}

button:hover::before {
  opacity: 1;
}

button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

button:active {
  transform: translateY(0);
}

#result-heading {
  text-align: center;
  margin-bottom: 1.5rem;
  font-size: 1.2rem;
  color: var(--text-dark);
}

/* Result Heading */
#result-heading {
  text-align: center;
  margin-bottom: var(--space-xl);
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

/* Meals Container */
.meals-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--space-xl);
  margin-bottom: var(--space-2xl);
}

.meal {
  background: var(--card-bg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow);
  overflow: hidden;
  transition: var(--transition);
  cursor: pointer;
  position: relative;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.meal::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
  opacity: 0;
  transition: var(--transition);
  z-index: 1;
  pointer-events: none;
}

.meal:hover::before {
  opacity: 1;
}

.meal:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

.meal img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: var(--transition);
}

.meal:hover img {
  transform: scale(1.05);
}

.meal-info {
  padding: var(--space-lg);
  position: relative;
  z-index: 2;
}

.meal-title {
  font-size: 1.125rem;
  font-weight: 700;
  margin-bottom: var(--space-sm);
  color: var(--text-primary);
  line-height: 1.4;
}

.meal-category {
  display: inline-block;
  background: var(--primary-light);
  color: var(--primary-solid);
  padding: var(--space-xs) var(--space-md);
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: var(--border-radius-xl);
  margin-bottom: var(--space-sm);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Meal Details */
#meal-details {
  background-color: var(--background);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  padding: 1.5rem;
  margin: 2rem 0;
}

.meal-details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.meal-details-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

#back-btn {
  background-color: transparent;
  color: var(--primary);
  border: 1px solid var(--primary);
  padding: 8px 16px;
}

#back-btn:hover {
  background-color: var(--primary);
  color: white;
}

/* Rating System */
.meal-rating {
  text-align: center;
  margin: 1.5rem 0;
  padding: 1rem;
  background-color: var(--background-light);
  border-radius: var(--border-radius);
}

.rating-stars {
  display: flex;
  justify-content: center;
  gap: 5px;
  margin-top: 0.5rem;
}

.star {
  font-size: 1.5rem;
  color: #ddd;
  cursor: pointer;
  transition: var(--transition);
}

.star.filled {
  color: #ffc107;
}

.star:hover {
  color: #ffc107;
  transform: scale(1.1);
}

/* Shopping List */
.shopping-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  margin-bottom: 8px;
  box-shadow: var(--shadow);
  transition: var(--transition);
}

.shopping-item.checked {
  opacity: 0.6;
}

.shopping-item.checked .shopping-item-text {
  text-decoration: line-through;
}

.shopping-item-label {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  flex: 1;
}

.shopping-item-label input[type="checkbox"] {
  margin: 0;
}

.remove-btn {
  background-color: var(--danger);
  color: white;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
}

.remove-btn:hover {
  background-color: #c82333;
  transform: scale(1.1);
}

/* Collections */
.collection-card {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  padding: 1.5rem;
  margin-bottom: 1rem;
  transition: var(--transition);
}

.collection-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.collection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.collection-header h3 {
  margin: 0;
  color: var(--primary);
}

.collection-actions {
  display: flex;
  gap: 0.5rem;
}

.collection-description {
  color: var(--text-dark);
  opacity: 0.8;
  margin-bottom: 1rem;
  font-style: italic;
}

.collection-stats {
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
  color: var(--text-dark);
  opacity: 0.7;
}

.collection-stats i {
  margin-right: 5px;
}

/* Empty States */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--text-dark);
  opacity: 0.6;
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
}

/* Headers for tabs */
.favorites-header,
.collections-header,
.shopping-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.favorites-header h2,
.collections-header h2,
.shopping-list-header h2 {
  margin: 0;
  color: var(--text-dark);
}

.shopping-list-actions {
  display: flex;
  gap: 0.5rem;
}

.meal-details-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.meal-details-img {
  width: 100%;
  max-width: 400px;
  border-radius: var(--border-radius);
  margin-bottom: 1.5rem;
}

.meal-details-title {
  font-size: 1.8rem;
  color: var(--primary);
  margin-bottom: 0.5rem;
  text-align: center;
}

.meal-details-category {
  margin-bottom: 1rem;
  text-align: center;
}

.meal-details-category span {
  background-color: var(--primary-light);
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
}

.meal-details-instructions {
  line-height: 1.7;
  margin-bottom: 1.5rem;
}

.meal-details-instructions h3 {
  margin-bottom: 0.5rem;
}

.ingredients-list {
  list-style-type: none;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 8px;
  margin-bottom: 1.5rem;
  width: 100%;
}

.ingredients-list li {
  display: flex;
  align-items: center;
  padding: 6px 10px;
  background-color: var(--background-light);
  border-radius: var(--border-radius);
  font-size: 0.9rem;
}

.ingredients-list li i {
  color: var(--secondary);
  margin-right: 8px;
}

.youtube-link {
  display: inline-block;
  background-color: #ff0000;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 600;
  margin-top: 1rem;
  font-size: 0.9rem;
}

.youtube-link i {
  margin-right: 8px;
}

/* Error Container */
#error-container {
  background: linear-gradient(
    135deg,
    var(--danger-light),
    rgba(239, 68, 68, 0.1)
  );
  border: 2px solid var(--danger);
  color: var(--danger);
  padding: var(--space-lg);
  border-radius: var(--border-radius-lg);
  text-align: center;
  margin-bottom: var(--space-xl);
  font-weight: 600;
  box-shadow: var(--shadow);
  backdrop-filter: blur(10px);
}

/* Filters */
.filters-container {
  background: var(--card-bg);
  border-radius: var(--border-radius-lg);
  padding: var(--space-xl);
  margin-bottom: var(--space-xl);
  box-shadow: var(--shadow);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.filters-container h3 {
  margin-bottom: var(--space-lg);
  color: var(--text-primary);
  font-size: 1.125rem;
  font-weight: 700;
}

.filters {
  display: flex;
  gap: var(--space-md);
  align-items: center;
  flex-wrap: wrap;
}

.filters select {
  min-width: 180px;
  background: var(--background);
  border: 2px solid rgba(0, 0, 0, 0.1);
}

.filters select:focus {
  border-color: var(--primary-solid);
  box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
}

/* Cooking Timer */
.cooking-timer {
  position: fixed;
  bottom: var(--space-xl);
  right: var(--space-xl);
  background: var(--card-bg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
  padding: var(--space-xl);
  min-width: 280px;
  z-index: 1000;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.timer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-lg);
}

.timer-header h3 {
  margin: 0;
  color: var(--primary-solid);
  font-size: 1.125rem;
  font-weight: 700;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-muted);
  transition: var(--transition);
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: var(--text-primary);
  background: var(--background-secondary);
}

.timer-display {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 800;
  background: var(--primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--space-lg);
  font-family: "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", monospace;
  letter-spacing: 0.1em;
}

.timer-controls {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.timer-controls input {
  text-align: center;
  font-weight: 600;
}

.timer-controls button {
  padding: var(--space-sm) var(--space-md);
  font-size: 0.875rem;
}

/* Modals */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: var(--background);
  border-radius: var(--border-radius);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--background-light);
}

.modal-header h3 {
  margin: 0;
  color: var(--text-dark);
}

.modal-body {
  padding: 1.5rem;
}

.modal-body input,
.modal-body textarea,
.modal-body select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: var(--border-radius);
  margin-bottom: 1rem;
  background-color: var(--background);
  color: var(--text-dark);
}

.modal-body textarea {
  resize: vertical;
  min-height: 80px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid var(--background-light);
}

/* Share Options */
.share-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.share-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 1rem;
  border: 1px solid #ddd;
  border-radius: var(--border-radius);
  background-color: var(--background);
  color: var(--text-dark);
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
}

.share-btn:hover {
  background-color: var(--background-light);
  transform: translateY(-2px);
}

.share-btn[data-platform="facebook"]:hover {
  background-color: #1877f2;
  color: white;
}

.share-btn[data-platform="twitter"]:hover {
  background-color: #1da1f2;
  color: white;
}

.share-btn[data-platform="whatsapp"]:hover {
  background-color: #25d366;
  color: white;
}

.share-url-container {
  margin-top: 1rem;
}

.share-url-container input {
  margin-bottom: 0;
}

/* Notifications */
.notification {
  position: fixed;
  top: var(--space-xl);
  right: var(--space-xl);
  background: linear-gradient(135deg, var(--success), #059669);
  color: white;
  padding: var(--space-lg) var(--space-xl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
  z-index: 1001;
  transform: translateX(calc(100% + var(--space-xl)));
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 600;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-width: 280px;
}

.notification.show {
  transform: translateX(0);
}

.notification::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
  border-radius: inherit;
}

/* Remove favorite button */
.remove-favorite-btn {
  background-color: var(--danger);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  padding: 6px 12px;
  font-size: 0.8rem;
  cursor: pointer;
  margin-top: 8px;
  transition: var(--transition);
}

.remove-favorite-btn:hover {
  background-color: #c82333;
}

.hidden {
  display: none;
}

@media (max-width: 768px) {
  .header-top {
    flex-direction: column;
    gap: 1rem;
  }

  .nav-tabs {
    flex-wrap: wrap;
  }

  .nav-tab {
    padding: 0.8rem 1rem;
    font-size: 0.9rem;
  }

  .quick-actions {
    flex-direction: column;
    align-items: center;
  }

  .filters {
    flex-direction: column;
    align-items: stretch;
  }

  .filters select {
    min-width: auto;
  }

  .meal-details-header {
    flex-direction: column;
    align-items: stretch;
  }

  .meal-details-actions {
    justify-content: center;
  }

  .favorites-header,
  .collections-header,
  .shopping-list-header {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }

  .collection-header {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
    gap: 1rem;
  }

  .share-options {
    grid-template-columns: repeat(2, 1fr);
  }

  .cooking-timer {
    bottom: 10px;
    right: 10px;
    left: 10px;
    min-width: auto;
  }

  .notification {
    right: 10px;
    left: 10px;
  }
}

@media (max-width: 600px) {
  .search-container {
    flex-direction: column;
  }

  .search-type-toggle {
    flex-direction: column;
  }

  .search-type-btn:first-child {
    border-radius: var(--border-radius) var(--border-radius) 0 0;
  }

  .search-type-btn:last-child {
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    border-left: 1px solid var(--primary);
    border-top: none;
  }

  button#search-btn {
    width: 100%;
    padding: 10px;
  }

  .meals-container {
    grid-template-columns: 1fr;
  }

  .meal-details-img {
    max-width: 100%;
  }

  .ingredients-list {
    grid-template-columns: 1fr;
  }

  .nav-tab {
    padding: 0.6rem 0.8rem;
    font-size: 0.8rem;
  }

  .action-btn {
    padding: 8px 12px;
    font-size: 0.8rem;
  }

  .share-options {
    grid-template-columns: 1fr;
  }

  .modal-content {
    width: 95%;
    margin: 1rem;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 1rem;
  }
}
