* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Color Palette */
:root {
  --primary: #ff7e5f;
  --primary-dark: #eb5e41;
  --primary-light: #ffb199;
  --secondary: #0ba360;
  --text-dark: #333333;
  --text-light: #f8f9fa;
  --background: #ffffff;
  --background-light: #f8f9fa;
  --card-bg: #ffffff;
  --border-radius: 8px;
  --shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

body {
  font-family: sans-serif;
  background-color: var(--background-light);
  color: var(--text-dark);
  line-height: 1.6;
}

.container {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

header {
  text-align: center;
  margin-bottom: 2rem;
}

h1 {
  font-size: 2.2rem;
  color: var(--primary);
  margin-bottom: 0.5rem;
}

h1 i {
  margin-right: 10px;
}

header p {
  color: var(--text-dark);
  opacity: 0.7;
}

.search-container {
  display: flex;
  margin-bottom: 2rem;
  gap: 10px;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

input[type="text"] {
  flex: 1;
  padding: 10px 16px;
  font-size: 1rem;
  border-radius: var(--border-radius);
  border: 1px solid #ddd;
  background-color: var(--background);
  color: var(--text-dark);
  outline: none;
}

input[type="text"]:focus {
  border-color: var(--primary);
}

button {
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  padding: 0 20px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
}

button:hover {
  background-color: var(--primary-dark);
}

#result-heading {
  text-align: center;
  margin-bottom: 1.5rem;
  font-size: 1.2rem;
  color: var(--text-dark);
}

.meals-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
}

.meal {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  overflow: hidden;
  transition: var(--transition);
  cursor: pointer;
  position: relative;
}

.meal:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}

.meal img {
  width: 100%;
  height: 180px;
  object-fit: cover;
}

.meal-info {
  padding: 1rem;
}

.meal-title {
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
  color: var(--text-dark);
}

.meal-category {
  display: inline-block;
  background-color: var(--primary-light);
  color: var(--text-dark);
  padding: 3px 8px;
  font-size: 0.8rem;
  border-radius: 20px;
  margin-bottom: 10px;
}

#meal-details {
  background-color: var(--background);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  padding: 1.5rem;
  margin: 2rem 0;
}

#back-btn {
  margin-bottom: 1.5rem;
  background-color: transparent;
  color: var(--primary);
  border: 1px solid var(--primary);
  padding: 8px 16px;
}

#back-btn:hover {
  background-color: var(--primary);
  color: white;
}

.meal-details-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.meal-details-img {
  width: 100%;
  max-width: 400px;
  border-radius: var(--border-radius);
  margin-bottom: 1.5rem;
}

.meal-details-title {
  font-size: 1.8rem;
  color: var(--primary);
  margin-bottom: 0.5rem;
  text-align: center;
}

.meal-details-category {
  margin-bottom: 1rem;
  text-align: center;
}

.meal-details-category span {
  background-color: var(--primary-light);
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
}

.meal-details-instructions {
  line-height: 1.7;
  margin-bottom: 1.5rem;
}

.meal-details-instructions h3 {
  margin-bottom: 0.5rem;
}

.ingredients-list {
  list-style-type: none;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 8px;
  margin-bottom: 1.5rem;
  width: 100%;
}

.ingredients-list li {
  display: flex;
  align-items: center;
  padding: 6px 10px;
  background-color: var(--background-light);
  border-radius: var(--border-radius);
  font-size: 0.9rem;
}

.ingredients-list li i {
  color: var(--secondary);
  margin-right: 8px;
}

.youtube-link {
  display: inline-block;
  background-color: #ff0000;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 600;
  margin-top: 1rem;
  font-size: 0.9rem;
}

.youtube-link i {
  margin-right: 8px;
}

#error-container {
  background-color: rgba(255, 126, 95, 0.1);
  border: 1px solid var(--primary);
  color: var(--primary-dark);
  padding: 1rem;
  border-radius: var(--border-radius);
  text-align: center;
  margin-bottom: 1.5rem;
}

.api-link {
  text-align: center;
  padding-bottom: 1.5rem;
  color: var(--text-dark);
  opacity: 0.7;
  font-size: 0.9rem;
}

.api-link a {
  color: var(--primary);
  text-decoration: none;
}

.hidden {
  display: none;
}

@media (max-width: 600px) {
  .search-container {
    flex-direction: column;
  }

  button#search-btn {
    width: 100%;
    padding: 10px;
  }

  .meals-container {
    grid-template-columns: 1fr;
  }

  .meal-details-img {
    max-width: 100%;
  }

  .ingredients-list {
    grid-template-columns: 1fr;
  }
}
