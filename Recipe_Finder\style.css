/*
===============================================
RECIPE FINDER - PROFESSIONAL STYLING
===============================================
Author: Recipe Finder Team
Version: 2.0
Description: Modern, professional recipe finder application
===============================================
*/

/* ===== IMPORTS ===== */
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap");

/* ===== RESET & BASE ===== */
*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* ===== CSS CUSTOM PROPERTIES ===== */
:root {
  /* === BRAND COLORS === */
  --brand-primary: #2563eb;
  --brand-primary-light: #3b82f6;
  --brand-primary-dark: #1d4ed8;
  --brand-secondary: #059669;
  --brand-secondary-light: #10b981;
  --brand-secondary-dark: #047857;
  --brand-accent: #dc2626;
  --brand-accent-light: #ef4444;

  /* === NEUTRAL PALETTE === */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* === SEMANTIC COLORS === */
  --color-success: #059669;
  --color-success-light: #d1fae5;
  --color-warning: #d97706;
  --color-warning-light: #fef3c7;
  --color-error: #dc2626;
  --color-error-light: #fee2e2;
  --color-info: #2563eb;
  --color-info-light: #dbeafe;

  /* === TYPOGRAPHY === */
  --font-primary: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI",
    sans-serif;
  --font-display: "Playfair Display", Georgia, serif;
  --font-mono: "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", monospace;

  /* === FONT WEIGHTS === */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;

  /* === FONT SIZES === */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;

  /* === SPACING SYSTEM === */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;

  /* === BORDER RADIUS === */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-full: 9999px;

  /* === SHADOWS === */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* === TRANSITIONS === */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);

  /* === Z-INDEX SCALE === */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* === LIGHT THEME === */
:root {
  --bg-primary: var(--gray-50);
  --bg-secondary: #ffffff;
  --bg-tertiary: var(--gray-100);
  --text-primary: var(--gray-900);
  --text-secondary: var(--gray-700);
  --text-tertiary: var(--gray-500);
  --border-primary: var(--gray-200);
  --border-secondary: var(--gray-300);
}

/* === DARK THEME === */
[data-theme="dark"] {
  --bg-primary: var(--gray-900);
  --bg-secondary: var(--gray-800);
  --bg-tertiary: var(--gray-700);
  --text-primary: var(--gray-50);
  --text-secondary: var(--gray-300);
  --text-tertiary: var(--gray-400);
  --border-primary: var(--gray-700);
  --border-secondary: var(--gray-600);

  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4),
    0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4),
    0 4px 6px -2px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4),
    0 10px 10px -5px rgba(0, 0, 0, 0.3);
}

/* ===== BASE STYLES ===== */
html {
  font-size: 16px;
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
}

body {
  font-family: var(--font-primary);
  font-size: var(--text-base);
  font-weight: var(--font-weight-normal);
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* ===== LAYOUT ===== */
.container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

@media (min-width: 640px) {
  .container {
    padding: 0 var(--space-6);
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 var(--space-8);
  }
}

/* ===== TYPOGRAPHY ===== */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-display);
  font-weight: var(--font-weight-bold);
  line-height: 1.2;
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

h1 {
  font-size: var(--text-4xl);
  font-weight: var(--font-weight-extrabold);
}

h2 {
  font-size: var(--text-3xl);
}

h3 {
  font-size: var(--text-2xl);
}

h4 {
  font-size: var(--text-xl);
}

h5 {
  font-size: var(--text-lg);
}

h6 {
  font-size: var(--text-base);
}

p {
  margin-bottom: var(--space-4);
  color: var(--text-secondary);
}

/* ===== UTILITIES ===== */
.hidden {
  display: none !important;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

/* ===== HEADER ===== */
header {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-lg);
  margin-bottom: var(--space-12);
  padding: var(--space-8);
  text-align: center;
  position: relative;
  overflow: hidden;
}

header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(
    90deg,
    var(--brand-primary),
    var(--brand-secondary),
    var(--brand-accent)
  );
}

.header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
}

.header-top h1 {
  font-family: var(--font-display);
  font-size: var(--text-4xl);
  font-weight: var(--font-weight-extrabold);
  color: var(--brand-primary);
  margin: 0;
  letter-spacing: -0.025em;
}

.header-top h1 i {
  margin-right: var(--space-3);
  color: var(--brand-secondary);
}

header p {
  font-size: var(--text-lg);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  margin: 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* ===== THEME TOGGLE ===== */
.theme-toggle {
  background: var(--bg-tertiary);
  border: 2px solid var(--border-secondary);
  border-radius: var(--radius-full);
  color: var(--text-primary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 48px;
  width: 48px;
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.theme-toggle:hover {
  background: var(--brand-primary);
  border-color: var(--brand-primary);
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.theme-toggle:active {
  transform: translateY(0);
}

.theme-toggle i {
  font-size: var(--text-lg);
  transition: var(--transition-fast);
}

/* ===== API LINK ===== */
.api-link {
  text-align: center;
  margin-bottom: var(--space-8);
  font-size: var(--text-sm);
  color: var(--text-tertiary);
}

.api-link a {
  color: var(--brand-primary);
  text-decoration: none;
  font-weight: var(--font-weight-semibold);
  transition: var(--transition-fast);
}

.api-link a:hover {
  color: var(--brand-primary-dark);
  text-decoration: underline;
}

/* ===== NAVIGATION ===== */
.nav-tabs {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  display: flex;
  gap: var(--space-1);
  justify-content: center;
  margin-bottom: var(--space-10);
  padding: var(--space-2);
}

.nav-tab {
  background: transparent;
  border: none;
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  font-size: var(--text-sm);
  font-weight: var(--font-weight-semibold);
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  position: relative;
  transition: var(--transition-fast);
  white-space: nowrap;
}

.nav-tab:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.nav-tab.active {
  background: var(--brand-primary);
  color: white;
  box-shadow: var(--shadow-sm);
}

.nav-tab i {
  font-size: var(--text-sm);
}

/* ===== TAB CONTENT ===== */
.tab-content {
  display: block;
}

.tab-content.hidden {
  display: none;
}

/* ===== FORM ELEMENTS ===== */
input[type="text"],
input[type="number"],
input[type="email"],
textarea,
select {
  background: var(--bg-secondary);
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  font-family: var(--font-primary);
  font-size: var(--text-base);
  font-weight: var(--font-weight-normal);
  padding: var(--space-3) var(--space-4);
  transition: var(--transition-fast);
  width: 100%;
}

input[type="text"]:focus,
input[type="number"]:focus,
input[type="email"]:focus,
textarea:focus,
select:focus {
  border-color: var(--brand-primary);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  outline: none;
}

input[type="text"]::placeholder,
input[type="number"]::placeholder,
input[type="email"]::placeholder,
textarea::placeholder {
  color: var(--text-tertiary);
}

textarea {
  min-height: 100px;
  resize: vertical;
}

/* ===== BUTTONS ===== */
button,
.btn {
  background: var(--brand-primary);
  border: none;
  border-radius: var(--radius-lg);
  color: white;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  font-family: var(--font-primary);
  font-size: var(--text-sm);
  font-weight: var(--font-weight-semibold);
  gap: var(--space-2);
  justify-content: center;
  padding: var(--space-3) var(--space-6);
  position: relative;
  text-decoration: none;
  transition: var(--transition-fast);
  white-space: nowrap;
}

button:hover,
.btn:hover {
  background: var(--brand-primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

button:active,
.btn:active {
  transform: translateY(0);
}

button:disabled,
.btn:disabled {
  background: var(--gray-300);
  color: var(--gray-500);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Button Variants */
.btn-secondary {
  background: var(--brand-secondary);
}

.btn-secondary:hover {
  background: var(--brand-secondary-dark);
}

.btn-outline {
  background: transparent;
  border: 2px solid var(--brand-primary);
  color: var(--brand-primary);
}

.btn-outline:hover {
  background: var(--brand-primary);
  color: white;
}

.btn-danger {
  background: var(--color-error);
}

.btn-danger:hover {
  background: #b91c1c;
}

.btn-small {
  font-size: var(--text-xs);
  padding: var(--space-2) var(--space-4);
}

.btn-large {
  font-size: var(--text-base);
  padding: var(--space-4) var(--space-8);
}

/* Search Options */
.search-options {
  margin-bottom: var(--space-xl);
  background: var(--card-bg);
  border-radius: var(--border-radius-lg);
  padding: var(--space-xl);
  box-shadow: var(--shadow);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.search-container {
  display: flex;
  margin-bottom: var(--space-lg);
  gap: var(--space-sm);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.search-type-toggle {
  display: flex;
  justify-content: center;
  margin: var(--space-lg) 0;
  background: var(--background-secondary);
  border-radius: var(--border-radius);
  padding: var(--space-xs);
  gap: var(--space-xs);
}

.search-type-btn {
  background: transparent;
  color: var(--text-secondary);
  border: none;
  padding: var(--space-sm) var(--space-lg);
  cursor: pointer;
  transition: var(--transition);
  font-size: 0.875rem;
  font-weight: 600;
  border-radius: var(--border-radius-sm);
  position: relative;
  overflow: hidden;
}

.search-type-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--primary);
  opacity: 0;
  transition: var(--transition);
  z-index: -1;
}

.search-type-btn.active::before {
  opacity: 1;
}

.search-type-btn.active {
  color: white;
  box-shadow: var(--shadow-sm);
}

.search-type-btn:hover:not(.active) {
  color: var(--primary-solid);
  background: var(--primary-light);
}

#ingredient-search-container {
  margin-top: var(--space-lg);
}

#ingredient-search-container input {
  width: 100%;
  margin-bottom: var(--space-sm);
}

/* Quick Actions */
.quick-actions {
  display: flex;
  justify-content: center;
  gap: var(--space-md);
  margin-bottom: var(--space-xl);
}

.action-btn {
  background: var(--secondary);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  padding: var(--space-md) var(--space-lg);
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.action-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
  opacity: 0;
  transition: var(--transition);
}

.action-btn:hover::before {
  opacity: 1;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.action-btn.secondary {
  background: var(--background-secondary);
  color: var(--text-primary);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.action-btn.secondary:hover {
  background: var(--background-tertiary);
}

.action-btn.danger {
  background: var(--danger);
}

.action-btn.danger:hover {
  background: #dc2626;
}

.action-btn.small {
  padding: var(--space-sm) var(--space-md);
  font-size: 0.75rem;
}

/* Filters */
.filters-container {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: var(--shadow);
}

.filters-container h3 {
  margin-bottom: 1rem;
  color: var(--text-dark);
}

.filters {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.filters select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: var(--border-radius);
  background-color: var(--background);
  color: var(--text-dark);
  font-size: 0.9rem;
  min-width: 150px;
}

.filters select:focus {
  outline: none;
  border-color: var(--primary);
}

/* Search History */
.search-history {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 1rem;
}

.history-item {
  background-color: var(--background-light);
  border: 1px solid #ddd;
  border-radius: 20px;
  padding: 6px 12px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: var(--transition);
  color: var(--text-dark);
}

.history-item:hover {
  background-color: var(--primary-light);
  border-color: var(--primary);
}

.search-container {
  display: flex;
  margin-bottom: 2rem;
  gap: 10px;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

/* Input Fields */
input[type="text"],
input[type="number"],
textarea,
select {
  flex: 1;
  padding: var(--space-md) var(--space-lg);
  font-size: 1rem;
  border-radius: var(--border-radius);
  border: 2px solid rgba(0, 0, 0, 0.1);
  background-color: var(--background);
  color: var(--text-primary);
  outline: none;
  transition: var(--transition);
  font-family: inherit;
  box-shadow: var(--shadow-sm);
}

input[type="text"]:focus,
input[type="number"]:focus,
textarea:focus,
select:focus {
  border-color: var(--primary-solid);
  box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
  transform: translateY(-1px);
}

input[type="text"]::placeholder,
input[type="number"]::placeholder,
textarea::placeholder {
  color: var(--text-muted);
}

/* Buttons */
button {
  background: var(--primary);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  padding: var(--space-md) var(--space-xl);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  font-family: inherit;
}

button::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
  opacity: 0;
  transition: var(--transition);
}

button:hover::before {
  opacity: 1;
}

button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

button:active {
  transform: translateY(0);
}

#result-heading {
  text-align: center;
  margin-bottom: 1.5rem;
  font-size: 1.2rem;
  color: var(--text-dark);
}

/* Result Heading */
#result-heading {
  text-align: center;
  margin-bottom: var(--space-xl);
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

/* Meals Container */
.meals-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--space-xl);
  margin-bottom: var(--space-2xl);
}

.meal {
  background: var(--card-bg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow);
  overflow: hidden;
  transition: var(--transition);
  cursor: pointer;
  position: relative;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.meal::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
  opacity: 0;
  transition: var(--transition);
  z-index: 1;
  pointer-events: none;
}

.meal:hover::before {
  opacity: 1;
}

.meal:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

.meal img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: var(--transition);
}

.meal:hover img {
  transform: scale(1.05);
}

.meal-info {
  padding: var(--space-lg);
  position: relative;
  z-index: 2;
}

.meal-title {
  font-size: 1.125rem;
  font-weight: 700;
  margin-bottom: var(--space-sm);
  color: var(--text-primary);
  line-height: 1.4;
}

.meal-category {
  display: inline-block;
  background: var(--primary-light);
  color: var(--primary-solid);
  padding: var(--space-xs) var(--space-md);
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: var(--border-radius-xl);
  margin-bottom: var(--space-sm);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Meal Details */
#meal-details {
  background-color: var(--background);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  padding: 1.5rem;
  margin: 2rem 0;
}

.meal-details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.meal-details-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

#back-btn {
  background-color: transparent;
  color: var(--primary);
  border: 1px solid var(--primary);
  padding: 8px 16px;
}

#back-btn:hover {
  background-color: var(--primary);
  color: white;
}

/* Rating System */
.meal-rating {
  text-align: center;
  margin: 1.5rem 0;
  padding: 1rem;
  background-color: var(--background-light);
  border-radius: var(--border-radius);
}

.rating-stars {
  display: flex;
  justify-content: center;
  gap: 5px;
  margin-top: 0.5rem;
}

.star {
  font-size: 1.5rem;
  color: #ddd;
  cursor: pointer;
  transition: var(--transition);
}

.star.filled {
  color: #ffc107;
}

.star:hover {
  color: #ffc107;
  transform: scale(1.1);
}

/* Shopping List */
.shopping-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  margin-bottom: 8px;
  box-shadow: var(--shadow);
  transition: var(--transition);
}

.shopping-item.checked {
  opacity: 0.6;
}

.shopping-item.checked .shopping-item-text {
  text-decoration: line-through;
}

.shopping-item-label {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  flex: 1;
}

.shopping-item-label input[type="checkbox"] {
  margin: 0;
}

.remove-btn {
  background-color: var(--danger);
  color: white;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
}

.remove-btn:hover {
  background-color: #c82333;
  transform: scale(1.1);
}

/* Collections */
.collection-card {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  padding: 1.5rem;
  margin-bottom: 1rem;
  transition: var(--transition);
}

.collection-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.collection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.collection-header h3 {
  margin: 0;
  color: var(--primary);
}

.collection-actions {
  display: flex;
  gap: 0.5rem;
}

.collection-description {
  color: var(--text-dark);
  opacity: 0.8;
  margin-bottom: 1rem;
  font-style: italic;
}

.collection-stats {
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
  color: var(--text-dark);
  opacity: 0.7;
}

.collection-stats i {
  margin-right: 5px;
}

/* Empty States */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--text-dark);
  opacity: 0.6;
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
}

/* Headers for tabs */
.favorites-header,
.collections-header,
.shopping-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.favorites-header h2,
.collections-header h2,
.shopping-list-header h2 {
  margin: 0;
  color: var(--text-dark);
}

.shopping-list-actions {
  display: flex;
  gap: 0.5rem;
}

.meal-details-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.meal-details-img {
  width: 100%;
  max-width: 400px;
  border-radius: var(--border-radius);
  margin-bottom: 1.5rem;
}

.meal-details-title {
  font-size: 1.8rem;
  color: var(--primary);
  margin-bottom: 0.5rem;
  text-align: center;
}

.meal-details-category {
  margin-bottom: 1rem;
  text-align: center;
}

.meal-details-category span {
  background-color: var(--primary-light);
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
}

.meal-details-instructions {
  line-height: 1.7;
  margin-bottom: 1.5rem;
}

.meal-details-instructions h3 {
  margin-bottom: 0.5rem;
}

.ingredients-list {
  list-style-type: none;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 8px;
  margin-bottom: 1.5rem;
  width: 100%;
}

.ingredients-list li {
  display: flex;
  align-items: center;
  padding: 6px 10px;
  background-color: var(--background-light);
  border-radius: var(--border-radius);
  font-size: 0.9rem;
}

.ingredients-list li i {
  color: var(--secondary);
  margin-right: 8px;
}

.youtube-link {
  display: inline-block;
  background-color: #ff0000;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 600;
  margin-top: 1rem;
  font-size: 0.9rem;
}

.youtube-link i {
  margin-right: 8px;
}

/* Error Container */
#error-container {
  background: linear-gradient(
    135deg,
    var(--danger-light),
    rgba(239, 68, 68, 0.1)
  );
  border: 2px solid var(--danger);
  color: var(--danger);
  padding: var(--space-lg);
  border-radius: var(--border-radius-lg);
  text-align: center;
  margin-bottom: var(--space-xl);
  font-weight: 600;
  box-shadow: var(--shadow);
  backdrop-filter: blur(10px);
}

/* Filters */
.filters-container {
  background: var(--card-bg);
  border-radius: var(--border-radius-lg);
  padding: var(--space-xl);
  margin-bottom: var(--space-xl);
  box-shadow: var(--shadow);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.filters-container h3 {
  margin-bottom: var(--space-lg);
  color: var(--text-primary);
  font-size: 1.125rem;
  font-weight: 700;
}

.filters {
  display: flex;
  gap: var(--space-md);
  align-items: center;
  flex-wrap: wrap;
}

.filters select {
  min-width: 180px;
  background: var(--background);
  border: 2px solid rgba(0, 0, 0, 0.1);
}

.filters select:focus {
  border-color: var(--primary-solid);
  box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
}

/* Cooking Timer */
.cooking-timer {
  position: fixed;
  bottom: var(--space-xl);
  right: var(--space-xl);
  background: var(--card-bg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
  padding: var(--space-xl);
  min-width: 280px;
  z-index: 1000;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.timer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-lg);
}

.timer-header h3 {
  margin: 0;
  color: var(--primary-solid);
  font-size: 1.125rem;
  font-weight: 700;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-muted);
  transition: var(--transition);
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: var(--text-primary);
  background: var(--background-secondary);
}

.timer-display {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 800;
  background: var(--primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--space-lg);
  font-family: "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", monospace;
  letter-spacing: 0.1em;
}

.timer-controls {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.timer-controls input {
  text-align: center;
  font-weight: 600;
}

.timer-controls button {
  padding: var(--space-sm) var(--space-md);
  font-size: 0.875rem;
}

/* Modals */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: var(--background);
  border-radius: var(--border-radius);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--background-light);
}

.modal-header h3 {
  margin: 0;
  color: var(--text-dark);
}

.modal-body {
  padding: 1.5rem;
}

.modal-body input,
.modal-body textarea,
.modal-body select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: var(--border-radius);
  margin-bottom: 1rem;
  background-color: var(--background);
  color: var(--text-dark);
}

.modal-body textarea {
  resize: vertical;
  min-height: 80px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid var(--background-light);
}

/* Share Options */
.share-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.share-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 1rem;
  border: 1px solid #ddd;
  border-radius: var(--border-radius);
  background-color: var(--background);
  color: var(--text-dark);
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
}

.share-btn:hover {
  background-color: var(--background-light);
  transform: translateY(-2px);
}

.share-btn[data-platform="facebook"]:hover {
  background-color: #1877f2;
  color: white;
}

.share-btn[data-platform="twitter"]:hover {
  background-color: #1da1f2;
  color: white;
}

.share-btn[data-platform="whatsapp"]:hover {
  background-color: #25d366;
  color: white;
}

.share-url-container {
  margin-top: 1rem;
}

.share-url-container input {
  margin-bottom: 0;
}

/* Notifications */
.notification {
  position: fixed;
  top: var(--space-xl);
  right: var(--space-xl);
  background: linear-gradient(135deg, var(--success), #059669);
  color: white;
  padding: var(--space-lg) var(--space-xl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
  z-index: 1001;
  transform: translateX(calc(100% + var(--space-xl)));
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 600;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-width: 280px;
}

.notification.show {
  transform: translateX(0);
}

.notification::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
  border-radius: inherit;
}

/* Remove favorite button */
.remove-favorite-btn {
  background-color: var(--danger);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  padding: 6px 12px;
  font-size: 0.8rem;
  cursor: pointer;
  margin-top: 8px;
  transition: var(--transition);
}

.remove-favorite-btn:hover {
  background-color: #c82333;
}

.hidden {
  display: none;
}

@media (max-width: 768px) {
  .header-top {
    flex-direction: column;
    gap: 1rem;
  }

  .nav-tabs {
    flex-wrap: wrap;
  }

  .nav-tab {
    padding: 0.8rem 1rem;
    font-size: 0.9rem;
  }

  .quick-actions {
    flex-direction: column;
    align-items: center;
  }

  .filters {
    flex-direction: column;
    align-items: stretch;
  }

  .filters select {
    min-width: auto;
  }

  .meal-details-header {
    flex-direction: column;
    align-items: stretch;
  }

  .meal-details-actions {
    justify-content: center;
  }

  .favorites-header,
  .collections-header,
  .shopping-list-header {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }

  .collection-header {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
    gap: 1rem;
  }

  .share-options {
    grid-template-columns: repeat(2, 1fr);
  }

  .cooking-timer {
    bottom: 10px;
    right: 10px;
    left: 10px;
    min-width: auto;
  }

  .notification {
    right: 10px;
    left: 10px;
  }
}

@media (max-width: 600px) {
  .search-container {
    flex-direction: column;
  }

  .search-type-toggle {
    flex-direction: column;
  }

  .search-type-btn:first-child {
    border-radius: var(--border-radius) var(--border-radius) 0 0;
  }

  .search-type-btn:last-child {
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    border-left: 1px solid var(--primary);
    border-top: none;
  }

  button#search-btn {
    width: 100%;
    padding: 10px;
  }

  .meals-container {
    grid-template-columns: 1fr;
  }

  .meal-details-img {
    max-width: 100%;
  }

  .ingredients-list {
    grid-template-columns: 1fr;
  }

  .nav-tab {
    padding: 0.6rem 0.8rem;
    font-size: 0.8rem;
  }

  .action-btn {
    padding: 8px 12px;
    font-size: 0.8rem;
  }

  .share-options {
    grid-template-columns: 1fr;
  }

  .modal-content {
    width: 95%;
    margin: 1rem;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 1rem;
  }
}
